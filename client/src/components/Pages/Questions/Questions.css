.question-details-page
{
    float: left;    
    width: calc(100% - 300px); 
    margin-top: 35px;
    padding: 0px;
}
.question-details-container, .ans-box
{
 margin-bottom: 0px;
 padding-bottom: 0px;  
}
.display-ans
{
  border-bottom: 1px solid rgba(0, 0, 0, 0.112);   
}
.question-details-container-2
{
  display: flex;  
}
.questions-votes
{
 padding: 5px 20px 5px 10px;
}
.questions-votes p
{
  margin: 0;
  font-size: 25px;
  text-align: center;  
}
.vote-icons 
{
  font-size: 40px;
  cursor: pointer;
  opacity: 0.6;
}
.vote-icons :active
{
  color: #ef8236;  
}
.question-details-container .question-body
{
  line-height: 22px;
  white-space: pre-line;   
}
.question-details-container .question-details-tags
{
 display: flex;
 justify-content: flex-start;
 align-items: center;
}
.question-details-container .question-details-tags p,
.post-ans-container p, .ans-tag
{
    padding: 5px;
    margin: 3px;
    font-size: 13px;
    border-radius: 2px;
    background-color: #e1ecf4;
    color: #39739d;
    text-decoration: none;
}
.question-action-user
{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
}
.question-action-user button, .edit-question-btn
{
  background-color: transparent;
  border: none;
  padding: 5px 0px;
  margin-right: 10px;
  text-decoration: none;
  color: #939292;
  cursor: pointer;
  font-size: 14px;
  transition: 0.3s;  
}
.question-action-user button:active
{
 border-bottom: 1px solid black;
}

.post-ans-container p{
    color: black;
    background-color: transparent;
    font-size: 13px;
}
.user-link
{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    text-decoration: none;
    height: 43px;
  
}
.user-link div {
    padding: 0px 5px;
}
.post-ans-container form textarea
{
    padding: 10px;
    border: 1px solid rgba(0, 0, 0, 0.3);
    width: 97%;
    font-family: 'Roboto', sans-serif;
    resize: vertical;
}
.post-ans-container form .post-ans-btn
{
   margin: 20px 0px;
   padding: 10px;
   background-color: #009dff;
   border-radius: 3px;
   transition: .3s;
   border: 1px solid #009dff;
   color: white; 
   cursor: pointer;
}
.post-ans-container form .post-ans-btn:hover{
    background-color: #0086d8;
}

.display-ans {
  padding-bottom: 20px;
  border-bottom: solid 1px rgba(0, 0, 0, 0.112);
}

.display-ans p {
  font-size: 14px;
  line-height: 18px;
  white-space: pre-line;
}


@media screen and (max-width: 1020px) {
  .question-details-page {
    width: calc(100% - 24px);
  }
}
