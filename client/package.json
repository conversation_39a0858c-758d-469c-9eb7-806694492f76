{"name": "my-app", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.2.1", "@fortawesome/free-solid-svg-icons": "^6.2.1", "@fortawesome/react-fontawesome": "^0.2.0", "@tanstack/react-query": "^5.0.0", "@tanstack/react-query-devtools": "^5.0.0", "axios": "^1.2.2", "jwt-decode": "^3.1.2", "moment": "^2.29.4", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-router-dom": "^6.6.1", "web-vitals": "^2.1.4"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.5.0", "vite": "^4.5.14", "vitest": "^3.1.4"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "build:netlify": "vite build && cp public/_redirects build/", "preview": "vite preview", "test": "vitest"}}